name: "\U0001F680 New feature proposal"
description: Suggest an idea for Pinia
labels: ['feature request']
body:
  - type: markdown
    attributes:
      value: |
        Thanks for your interest in the project and taking the time to fill out this feature report!
  - type: textarea
    id: feature-description
    attributes:
      label: What problem is this solving
      description: 'A clear and concise description of what the problem is. Ex. when using the function X we cannot do Y.'
    validations:
      required: true
  - type: textarea
    id: proposed-solution
    attributes:
      label: Proposed solution
      description: 'A clear and concise description of what you want to happen with an API proposal when applicable'
    validations:
      required: true
  - type: textarea
    id: alternative
    attributes:
      label: Describe alternatives you've considered
      description: A clear and concise description of any alternative solutions or features you've considered.
  - type: markdown
    attributes:
      value: |
        ## Before creating a feature request make sure that:
        - This hasn't been [requested before](https://github.com/vuejs/pinia/issues).
