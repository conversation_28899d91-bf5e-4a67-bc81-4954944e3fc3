name: ci

on:
  push:
    paths-ignore:
      - 'packages/docs/**'
      - 'packages/playground/**'
  pull_request:
    branches: [v2, v3]
    paths-ignore:
      - 'packages/docs/**'
      - 'packages/playground/**'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: pnpm

      - run: pnpm install --frozen-lockfile
      - run: pnpm run lint
      - run: pnpm run test:types
      - run: pnpm run -r dev:prepare
      - run: pnpm run test:vitest
      - run: pnpm run build
      - run: pnpm run build:dts
      - run: pnpm run test:dts
      - run: pnpm run size

      - uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
