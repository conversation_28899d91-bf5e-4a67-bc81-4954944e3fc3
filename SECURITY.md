# Security Policy

## Supported Versions

This is the list of versions of Pinia which are
currently being supported with security updates.

| Version   | Supported          |
| --------- | ------------------ |
| 2.2.x     | :white_check_mark: |
| &lt;2.2.0 | :x:                |

## Reporting a Vulnerability

To report a vulnerability, please email the details to <<EMAIL>>.

When a vulnerability is reported, it immediately becomes our top concern, with a full-time contributor dropping everything to work on it.

While discovering new vulnerabilities is rare, we recommend always using the latest versions of Vue, Pinia, and its official companion libraries to ensure your application remains as secure as possible.

Thanks for helping to keep Pinia secure.
