{"name": "@pinia/root", "packageManager": "pnpm@10.12.4", "type": "module", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "vitest --coverage --ui", "release": "node scripts/release.mjs", "size": "pnpm run -r size", "build": "pnpm run -C packages/pinia build && pnpm run -C packages/nuxt build && pnpm run -C packages/testing build", "docs": "pnpm run --filter ./packages/docs -r docs", "docs:api": "pnpm run --filter ./packages/docs -r docs:api", "docs:translation:compare": "pnpm run --filter ./packages/docs -r docs:translation:compare", "docs:translation:update": "pnpm run --filter ./packages/docs -r docs:translation:update", "docs:translation:status": "pnpm run --filter ./packages/docs -r docs:translation:status", "docs:build": "pnpm run docs:api && pnpm run --filter ./packages/docs -r docs:build", "docs:preview": "pnpm run --filter ./packages/docs -r docs:preview", "play": "pnpm run -r play", "build:dts": "pnpm run -r --parallel build:dts", "lint": "prettier -c --parser typescript \"packages/*/{src,__tests__,e2e}/**/*.[jt]s?(x)\" \"packages/docs/**/*.[jt]s\"", "lint:fix": "pnpm run lint --write", "test": "pnpm run -r dev:prepare && pnpm run test:types && pnpm run test:vitest run && pnpm run -r test && pnpm run build && pnpm run build:dts && pnpm test:dts", "test:vitest": "vitest --coverage", "test:types": "tsc --build ./tsconfig.json", "test:dts": "pnpm run -r test:dts", "postinstall": "simple-git-hooks"}, "devDependencies": {"@posva/prompts": "^2.4.4", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-commonjs": "^28.0.6", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@types/lodash.kebabcase": "^4.1.9", "@types/node": "^24.0.8", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "@vue/compiler-sfc": "~3.5.17", "@vue/server-renderer": "~3.5.17", "chalk": "^5.4.1", "conventional-changelog-cli": "^2.2.2", "execa": "^9.6.0", "globby": "^14.1.0", "happy-dom": "^18.0.1", "lint-staged": "^16.1.2", "lodash.kebabcase": "^4.1.1", "minimist": "^1.2.8", "p-series": "^3.0.0", "pascalcase": "^2.0.0", "prettier": "^3.6.2", "rimraf": "^6.0.1", "rollup": "^4.44.1", "rollup-plugin-typescript2": "^0.36.0", "semver": "^7.7.2", "simple-git-hooks": "^2.13.0", "typedoc": "^0.27.9", "typedoc-plugin-markdown": "~4.4.2", "typescript": "~5.8.3", "vitest": "^3.2.4", "vue": "~3.5.17"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged", "commit-msg": "node scripts/verifyCommit.mjs"}, "lint-staged": {"*.{js,mjs,json,cjs}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "resolutions": {"@nuxt/kit": "^3.9.0", "@nuxt/schema": "^3.9.0"}, "pnpm": {"onlyBuiltDependencies": ["esbuild", "simple-git-hooks", "vue-demi"], "ignoredBuiltDependencies": ["@parcel/watcher"]}}