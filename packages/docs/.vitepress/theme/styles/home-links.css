/* Style to get the cheat sheet link in the home page */

a.cta {
  text-align: center;
  border-radius: 8px;
}

a.cta:hover {
  border-color: var(--vp-c-brand);
  background-color: var(--c-bg-accent);
}

a.cta.vue-mastery::before {
  content: '';
  display: inline-block;
  width: 25px;
  height: 25px;
  background-image: url('https://firebasestorage.googleapis.com/v0/b/vue-mastery.appspot.com/o/flamelink%2Fmedia%2Fvue-mastery-logo-small.png?alt=media&token=941fcc3a-2b6f-40e9-b4c8-56b3890da108');
  background-size: 25px;
  background-repeat: no-repeat;
  background-position: bottom;
  margin-right: 0.5em;
}

a.cta.mastering-pinia {
  height: 100%;
  line-height: 100%;
  display: flex;
  justify-content: center;
  white-space: pre;
  min-height: 41px;
  position: relative;
  background-color: var(--c-black);
}
a.cta.mastering-pinia::before {
  content: '';
  width: 156px;
  height: 100%;
  display: inline-block;
  background-image: url('/mp-logo.svg');
  background-size: 156px;
  background-repeat: no-repeat;
  vertical-align: bottom;
  line-height: normal;
  transform: translateY(6px);
}
a.cta.mastering-pinia:hover::after {
  animation: none;
}
a.cta.mastering-pinia::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* background-color: var(--vp-button-brand-border); */
  border: 1px solid var(--vp-button-brand-border);
  border-radius: 20px;
  animation: ping 3s cubic-bezier(0, 0, 0.2, 1) infinite;
  z-index: -1;
}

@keyframes ping {
  15%,
  to {
    transform: scale(1.25, 2);
    opacity: 0;
  }
}

a.cta.vueschool {
  position: relative;
  color: currentColor;
  padding-left: 38px !important;
}
a.cta.vueschool::before {
  content: '';
  position: absolute;
  display: block;
  width: 20px;
  height: 20px;
  top: calc(50% - 10px);
  left: 12px;
  border-radius: 50%;
  border: 1px solid currentColor;
}

a.cta.vueschool::after {
  content: '';
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  top: calc(50% - 4px);
  left: 20px;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-left: 7px solid currentColor;
}

a.cta.rulekit {
  font-family: 'JetBrains Mono', ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
}

a.cta.rulekit::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url('/rulekit-logo.svg');
  background-size: 16px;
  background-repeat: no-repeat;
  background-position: center;
  margin-right: 0.5em;
  vertical-align: middle;
  filter: brightness(0); /* Make it black by default */
}

html.dark a.cta.rulekit::before {
  filter: brightness(0) invert(1); /* Make it white in dark mode */
}

@media (max-width: 420px) {
  a.cta.cta.vue-mastery {
    max-width: 320px;
    line-height: 1.5em;
    white-space: normal;
    display: block;
    padding-bottom: 10px;
    margin-left: 8px;
    margin-right: 8px;
  }
}
