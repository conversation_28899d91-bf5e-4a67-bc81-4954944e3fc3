/**
 * Colors
 * -------------------------------------------------------------------------- */

:root {
  --c-yellow-1: #ffd859;
  --c-yellow-2: #f7d336;
  --c-yellow-3: #dec96e;
  --c-yellow-soft-1: #ecb732;
  --c-yellow-soft-2: #c99513;

  --c-teal: #086367;
  --c-teal-light: #33898d;

  --c-white-dark: #f8f8f8;
  --c-black-darker: #0d121b;
  --c-black: #111827;
  --c-black-light: #161f32;
  --c-black-lighter: #262a44;

  --c-green-1: #52ce63;
  --c-green-2: #8ae99c;
  --c-green-3: #51a256;
  --c-green-soft: #316334;

  /* light theme is a bit different */
  --vp-c-brand-1: var(--vp-c-green-1);
  --vp-c-brand-2: var(--vp-c-green-2);
  --vp-c-brand-3: var(--vp-c-green-3);
  --vp-c-brand-soft: var(--vp-c-green-soft);

  --c-text-dark-1: #d9e6eb;
  --c-text-dark-2: #c4dde6;
  --c-text-dark-3: #abc4cc;
  --c-text-light-1: #2c3e50;
  --c-text-light-2: #476582;
  --c-text-light-3: #90a4b7;

  --vp-c-brand-dark: var(--c-green-soft);
  --vp-c-brand-darker: var(--c-green-soft);
  --vp-c-brand-dimm: rgba(100, 108, 255, 0.08);
  --vp-c-brand-text: var(--c-text-light-1);
  --c-bg-accent: var(--c-white-dark);
  --code-bg-color: var(--c-white-dark);
  --code-inline-bg-color: var(--c-white-dark);
  --code-font-family: 'dm', source-code-pro, Menlo, Monaco, Consolas,
    'Courier New', monospace;
  --code-font-size: 16px;

  --vp-code-block-bg: var(--vp-c-bg-alt);
  --vp-code-line-highlight-color: rgba(0, 0, 0, 0.075);
  --vp-code-color: var(--vp-text-color);
}

html.dark:root {
  /* --c-black: #ffffff;
  --c-white: #000000; */
  /* --c-divider-light: rgba(60, 60, 67, 0.12);
  --c-divider-dark: rgba(84, 84, 88, 0.48); */
  /* --c-brand-light: var(--c-yellow-light); */

  --vp-c-brand-1: var(--c-yellow-1);
  --vp-c-brand-2: var(--c-yellow-2);
  --vp-c-brand-3: var(--c-yellow-3);

  --vp-c-bg-alpha-with-backdrop: rgba(20, 25, 36, 0.7);
  --vp-c-bg-alpha-without-backdrop: rgba(20, 25, 36, 0.9);

  --vp-code-line-highlight-color: rgba(0, 0, 0, 0.5);

  --vp-c-text-1: var(--c-text-dark-1);
  --vp-c-brand-text: var(--c-text-light-1);
  --c-text-light: var(--c-text-dark-2);
  --c-text-lighter: var(--c-text-dark-3);
  --c-divider: var(--c-divider-dark);
  --c-bg-accent: var(--c-black-light);
  /* --vp-code-inline-bg: var(--vp-c-black-light); */

  --vp-c-bg: var(--c-black);
  --vp-c-bg-soft: var(--c-black-light);
  --vp-c-bg-soft-up: var(--c-black-lighter);
  --vp-c-bg-mute: var(--c-black-light);
  --vp-c-bg-soft-mute: var(--c-black-lighter);
  --vp-c-bg-alt: #0d121b;
  --vp-c-bg-elv: var(--vp-c-bg-soft);
  --vp-c-bg-elv-mute: var(--vp-c-bg-soft-mute);
  --vp-c-mute: var(--vp-c-bg-mute);
  --vp-c-mute-dark: var(--c-black-lighter);
  --vp-c-mute-darker: var(--c-black-darker);

  --vp-home-hero-name-background: -webkit-linear-gradient(
    78deg,
    var(--c-yellow-2) 30%,
    var(--c-green-2)
  );
}

html.dark .DocSearch {
  --docsearch-hit-active-color: var(--c-text-light-1);
}

/**
 * Component: Button
 * -------------------------------------------------------------------------- */

:root {
  --vp-button-brand-border: var(--c-yellow-soft-1);
  --vp-button-brand-text: var(--c-black);
  --vp-button-brand-bg: var(--c-yellow-1);
  --vp-button-brand-hover-border: var(--c-yellow-2);
  --vp-button-brand-hover-text: var(--c-black-darker);
  --vp-button-brand-hover-bg: var(--c-yellow-2);
  --vp-button-brand-active-border: var(--c-yellow-soft-1);
  --vp-button-brand-active-text: var(--c-black-darker);
  --vp-button-brand-active-bg: var(--vp-button-brand-bg);
}

/**
 * Component: Home
 * -------------------------------------------------------------------------- */

:root {
  --vp-home-hero-name-color: transparent;
  --vp-home-hero-name-background: linear-gradient(
    292deg,
    var(--c-green-1) 50%,
    var(--c-green-2)
  );
  --vp-home-hero-image-background-image: linear-gradient(
    15deg,
    var(--c-yellow-2) 65%,
    var(--c-green-1) 30%
  );
  --vp-home-hero-image-filter: blur(40px);
}

.VPHero .VPImage.image-src {
  max-height: 192px;
}

@media (min-width: 640px) {
  :root {
    --vp-home-hero-image-filter: blur(56px);
  }
  .VPHero .VPImage.image-src {
    max-height: 256px;
  }
}

@media (min-width: 960px) {
  :root {
    --vp-home-hero-image-filter: blur(72px);
  }
  .VPHero .VPImage.image-src {
    max-height: 320px;
  }
}

.become-sponsor {
  font-size: 0.9em;
  font-weight: 700;
  width: auto;
  text-align: center;
  background-color: transparent;
  padding: 0.75em 2em;
  border-radius: 2em;
  transition: all 0.15s ease;
  box-sizing: border-box;
  border: 2px solid var(--c-yellow-2);
}

.become-sponsor.become-sponsor:hover {
  background-color: var(--c-yellow-1);
  text-decoration: none;
  border-color: var(--c-yellow-1);
  color: var(--c-text-light-1);
}

.vp-doc a {
  text-decoration: none;
}
.vp-doc a:hover {
  text-decoration: underline;
}

.sponsors-top .become-sponsor {
  font-size: 0.75em;
  padding: 0.2em;
  width: auto;
  max-width: 150px;
}

kbd {
  display: inline-block;
  padding: 3px 5px;
  font-size: 0.65em;
  color: var(--vp-c-text-1);
  vertical-align: middle;
  background-color: var(--vp-c-bg-mute);
  border: solid 1px var(--vp-c-bg-soft-mute);
  border-radius: 6px;
  box-shadow: inset 0 -1px 0 var(--vp-c-bg-soft-mute);
  line-height: 0.95em;
}
