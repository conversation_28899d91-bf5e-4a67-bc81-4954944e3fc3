# Cookbook

<RuleKitLink />

- [Migrating from Vuex ≤4](./migration-vuex.md): A migration guide for converting Vuex ≤4 projects.
- [HMR](./hot-module-replacement.md): How to activate hot module replacement and improve the developer experience.
- [Testing Stores (WIP)](./testing.md): How to unit test Stores and mock them in component unit tests.
- [Composing Stores](./composing-stores.md): How to cross use multiple stores. e.g. using the user store in the cart store.
- [Options API](./options-api.md): How to use Pinia without the composition API, outside of `setup()`.
- [Migrating from 0.0.7](./migration-0-0-7.md): A migration guide with more examples than the changelog.
