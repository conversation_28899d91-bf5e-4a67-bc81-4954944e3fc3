{"name": "@pinia/docs", "version": "0.0.0", "private": true, "type": "module", "scripts": {"predocs": "node run-typedoc.mjs", "docs": "vitepress dev .", "docs:api": "node run-typedoc.mjs", "docs:translation:compare": "v-translation compare", "docs:translation:update": "v-translation update", "docs:translation:status": "v-translation status", "docs:build": "vitepress build .", "docs:preview": "vitepress preview ."}, "dependencies": {"@chenfengyuan/vue-countdown": "^2.1.3", "@vueuse/core": "^13.4.0", "pinia": "workspace:*", "typedoc-vitepress-theme": "^1.1.2", "vitepress": "1.6.3", "vitepress-translation-helper": "^0.2.2", "vue-use-spring": "^0.3.3"}}