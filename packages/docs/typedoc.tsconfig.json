{"include": ["../pinia/src/global.d.ts", "../*/src/**/*.ts"], "exclude": ["../test-vue-2", "../pinia/__tests__/test-utils.ts", "../pinia/test-dts", "../*/__tests__/**/*.ts", "../*/src/*.spec.ts", "../nuxt/playground", "../playground", "../online-playground", "../nuxt/src/runtime", "**/*.spec.ts"], "compilerOptions": {"baseUrl": ".", "rootDir": "..", "outDir": "dist", "sourceMap": false, "noEmit": true, "paths": {"@pinia/*": ["../*/src"], "pinia": ["../pinia/src"]}, "target": "esnext", "module": "esnext", "moduleResolution": "node", "allowJs": false, "skipLibCheck": true, "noUnusedLocals": true, "strictNullChecks": true, "noImplicitAny": true, "noImplicitThis": true, "noImplicitReturns": false, "strict": true, "isolatedModules": true, "experimentalDecorators": true, "esModuleInterop": true, "removeComments": false, "jsx": "preserve", "lib": ["esnext", "dom"], "types": ["vitest", "node", "vite/client"]}}