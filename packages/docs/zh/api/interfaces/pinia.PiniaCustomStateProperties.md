---
sidebar: 'auto'
editLinks: false
sidebarDepth: 3
---

[API 文档](../index.md) / [pinia](../modules/pinia.md) / PiniaCustomStateProperties

# 接口：PiniaCustomStateProperties<S\> %{#interface-piniacustomstateproperties-s}%

[pinia](../modules/pinia.md).PiniaCustomStateProperties

通过 `pinia.use()` 添加到每个 `store.$state` 的属性。

## 类型参数 %{#type-parameters}%

| 名称 | 类型                                                                                                |
| :--- | :-------------------------------------------------------------------------------------------------- |
| `S`  | extends [`StateTree`](../modules/pinia.md#statetree) = [`StateTree`](../modules/pinia.md#statetree) |
