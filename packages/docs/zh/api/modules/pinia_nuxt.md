---
sidebar: 'auto'
editLinks: false
sidebarDepth: 3
---

[API 文档](../index.md) / @pinia/nuxt

# 模块: @pinia/nuxt %{#module-pinia-nuxt}%

## 接口 %{#interfaces}%

- [ModuleOptions](../interfaces/pinia_nuxt.ModuleOptions.md)

## 函数 %{#functions}%

### 默认值 %{#default}%

▸ **default**(`this`, `inlineOptions`, `nuxt`): `void` \| `Promise`<`void`\>

#### 参数

| Name            | Type                                                         |
| :-------------- | :----------------------------------------------------------- |
| `this`          | `void`                                                       |
| `inlineOptions` | [`ModuleOptions`](../interfaces/pinia_nuxt.ModuleOptions.md) |
| `nuxt`          | `Nuxt`                                                       |

#### 返回值

`void` \| `Promise`<`void`\>
