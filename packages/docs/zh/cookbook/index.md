# 手册 %{#cookbook}%

<RuleKitLink />

- [从 Vuex ≤4 迁移](./migration-vuex.md)。用于转换 Vuex ≤4 项目的迁移指南。
- [HMR](./hot-module-replacement.md)：如何激活热更新并改善开发者体验。
- [测试 Stores (WIP)](./testing.md): 如何对 Store 进行单元测试并在组件单元测试中模拟它们。
- [Composing Stores](./composing-stores.md): 如何交叉使用多个 store，例如在购物车 store 中使用用户 store。
- [选项式 API](./options-api.md): 如何在 `setup()` 外部使用 Pinia 而不使用组合式 API。
- [从 0.0.7 迁移](./migration-0-0-7.md)。迁移指南，比更新日志有更多的例子。
