{"name": "@pinia/eslint-plugin", "version": "0.1.0", "description": "ESLint plugin for Pinia best practices", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "rollup -c", "test": "vitest", "test:types": "tsc --noEmit"}, "keywords": ["eslint", "eslint-plugin", "pinia", "vue", "store", "state-management"], "author": "<PERSON>", "license": "MIT", "dependencies": {"@typescript-eslint/utils": "^6.0.0"}, "devDependencies": {"@typescript-eslint/parser": "^6.0.0", "@typescript-eslint/rule-tester": "^6.0.0", "eslint": "^8.0.0", "typescript": "^5.0.0", "vitest": "^1.0.0"}, "peerDependencies": {"eslint": "^8.0.0"}, "engines": {"node": ">=16"}}