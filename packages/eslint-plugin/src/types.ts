/**
 * @fileoverview Type definitions for Pinia ESLint plugin
 */

import type { TSESTree } from '@typescript-eslint/utils'

/**
 * Options for the prefer-use-store-naming rule
 */
export interface PreferUseStoreNamingOptions {
  /** Pattern for store function names (default: 'use*Store') */
  pattern?: string
  /** Whether to enforce camelCase (default: true) */
  camelCase?: boolean
}

/**
 * Information about a store definition in the AST
 */
export interface StoreDefinitionInfo {
  /** The defineStore call node */
  node: TSESTree.CallExpression
  /** Store ID if available */
  id: string | null
  /** Variable name the store is assigned to */
  variableName: string | null
  /** Whether this is a setup store */
  isSetupStore: boolean
  /** The setup function if this is a setup store */
  setupFunction: TSESTree.FunctionExpression | TSESTree.ArrowFunctionExpression | null
}

/**
 * Information about computed property usage
 */
export interface ComputedUsageInfo {
  /** The computed call node */
  node: TSESTree.CallExpression
  /** The getter function */
  getter: TSESTree.FunctionExpression | TSESTree.ArrowFunctionExpression | null
}

/**
 * Information about declared variables and functions in a setup store
 */
export interface SetupStoreDeclarations {
  /** Names of declared variables */
  variables: string[]
  /** Names of declared functions */
  functions: string[]
  /** All declared names combined */
  all: string[]
}

/**
 * Information about exported properties from a setup store
 */
export interface SetupStoreExports {
  /** Names of exported properties */
  properties: string[]
  /** The return statement node */
  returnStatement: TSESTree.ReturnStatement | null
  /** Whether the return object uses spread syntax */
  hasSpread: boolean
}

/**
 * Store dependency information for circular dependency detection
 */
export interface StoreDependency {
  /** Name of the store that has the dependency */
  storeName: string
  /** Names of stores this store depends on */
  dependencies: string[]
  /** AST nodes where dependencies are used */
  usageNodes: TSESTree.CallExpression[]
}

/**
 * Plugin configuration options
 */
export interface PluginConfig {
  /** Rules configuration */
  rules?: {
    'require-setup-store-properties-export'?: 'error' | 'warn' | 'off'
    'no-circular-store-dependencies'?: 'error' | 'warn' | 'off'
    'prefer-use-store-naming'?:
      | 'error'
      | 'warn'
      | 'off'
      | [string, PreferUseStoreNamingOptions]
    'no-store-in-computed'?: 'error' | 'warn' | 'off'
  }
}
