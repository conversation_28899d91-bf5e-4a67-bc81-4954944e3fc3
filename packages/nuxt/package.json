{"name": "@pinia/nuxt", "version": "0.11.2", "description": "<PERSON><PERSON>t <PERSON> for pinia", "keywords": ["pinia", "nuxt", "vue", "vuex", "store"], "homepage": "https://pinia.vuejs.org/ssr/nuxt.html", "bugs": {"url": "https://github.com/vuejs/pinia/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/pinia.git"}, "funding": "https://github.com/sponsors/posva", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "sideEffects": false, "type": "module", "exports": {".": "./dist/module.mjs"}, "main": "./dist/module.mjs", "types": "./dist/module.d.mts", "files": ["dist"], "scripts": {"build": "pnpm run dev:prepare && nuxt-module-build build", "dev": "nuxi dev playground", "dev:build": "nuxi build playground", "dev:prepare": "nuxt-module-build build --stub . && nuxi prepare playground", "test:types": "pnpm dev:prepare && nuxi typecheck", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . -l @pinia/nuxt -r 1"}, "dependencies": {"@nuxt/kit": "^3.9.0"}, "peerDependencies": {"pinia": "workspace:^"}, "devDependencies": {"@nuxt/module-builder": "1.0.1", "@nuxt/schema": "^3.9.0", "@nuxt/test-utils": "^3.19.1", "nuxt": "^3.17.5", "pinia": "workspace:^", "typescript": "^5.8.3", "vue-tsc": "^2.2.10"}, "publishConfig": {"access": "public"}}