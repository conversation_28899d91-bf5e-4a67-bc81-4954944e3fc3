{"$schema": "https://developer.microsoft.com/json-schemas/api-extractor/v7/api-extractor.schema.json", "mainEntryPointFilePath": "./dist/packages/pinia/src/index.d.ts", "apiReport": {"enabled": true, "reportFolder": "<projectFolder>/temp/"}, "docModel": {"enabled": true}, "dtsRollup": {"enabled": true, "publicTrimmedFilePath": "./dist/<unscopedPackageName>.d.ts"}, "tsdocMetadata": {"enabled": false}, "messages": {"compilerMessageReporting": {"default": {"logLevel": "warning"}}, "extractorMessageReporting": {"default": {"logLevel": "warning", "addToApiReportFile": true}, "ae-missing-release-tag": {"logLevel": "none"}, "ae-incompatible-release-tags": {"logLevel": "none"}}, "tsdocMessageReporting": {"default": {"logLevel": "warning"}, "tsdoc-code-span-missing-delimiter": {"logLevel": "none"}}}}