{"name": "@pinia/playground", "version": "0.0.0", "type": "module", "private": true, "scripts": {"play": "vite", "play:build": "vite build", "serve": "vite preview"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7"}, "dependencies": {"@vueuse/core": "^13.4.0", "mande": "^2.0.9", "pinia": "workspace:*", "swrv": "^1.1.0", "vue-promised": "^2.2.0", "vue-router": "^4.5.1"}}