### [1.0.2](https://github.com/vuejs/pinia/compare/@pinia/testing@1.0.1...@pinia/testing@1.0.2) (2025-06-04)

No code changes in this release.

### [1.0.1](https://github.com/vuejs/pinia/compare/@pinia/testing@1.0.0...@pinia/testing@1.0.1) (2025-04-09)

### Bug Fixes

- consistent computation of computed in tests with storeToRefs ([417db7a](https://github.com/vuejs/pinia/commit/417db7aacb35b98ebe4274fd43bba593eaa583df)), closes [#2913](https://github.com/vuejs/pinia/issues/2913)

## [1.0.0](https://github.com/vuejs/pinia/compare/@pinia/testing@0.1.7...@pinia/testing@1.0.0) (2025-02-11)

The testing package has been stable for a long time so it was time to have a v1!

### Features

- **testing:** warn about incorrect createSpy ([394f655](https://github.com/vuejs/pinia/commit/394f6553d13f2b46c6e52a68145c24699b98e7fa)), closes [#2896](https://github.com/vuejs/pinia/issues/2896)

## [0.1.7](https://github.com/vuejs/pinia/compare/@pinia/testing@0.1.6...@pinia/testing@0.1.7) (2024-11-03)

No code changes in this release.

## [0.1.6](https://github.com/vuejs/pinia/compare/@pinia/testing@0.1.5...@pinia/testing@0.1.6) (2024-09-30)

No code changes in this release.

## [0.1.5](https://github.com/vuejs/pinia/compare/@pinia/testing@0.1.4...@pinia/testing@0.1.5) (2024-08-06)

No code changes in this release.

## [0.1.4](https://github.com/vuejs/pinia/compare/@pinia/testing@0.1.4-beta.0...@pinia/testing@0.1.4) (2024-07-26)

No code changes in this release.

## [0.1.4-beta.0](https://github.com/vuejs/pinia/compare/@pinia/testing@0.1.3...@pinia/testing@0.1.4-beta.0) (2024-04-17)

### Bug Fixes

- **types:** use declare module vue ([8a6ce86](https://github.com/vuejs/pinia/commit/8a6ce86db83b6315c067c8a98c898b3c74efe62e))

## [0.1.3](https://github.com/vuejs/pinia/compare/@pinia/testing@0.1.3...@pinia/testing@0.1.3) (2024-04-04)

### Bug Fixes

- **types:** use declare module vue ([8a6ce86](https://github.com/vuejs/pinia/commit/8a6ce86db83b6315c067c8a98c898b3c74efe62e))

## [0.1.3](https://github.com/vuejs/pinia/compare/@pinia/testing@0.1.2...@pinia/testing@0.1.3) (2023-07-26)

No code changes in this release.

## [0.1.2](https://github.com/vuejs/pinia/compare/@pinia/testing@0.1.1...@pinia/testing@0.1.2) (2023-05-18)

- Force vue-demi version

## [0.1.1](https://github.com/vuejs/pinia/compare/@pinia/testing@0.1.0...@pinia/testing@0.1.1) (2023-05-17)

No code changes in this release.

# [0.1.0](https://github.com/vuejs/pinia/compare/@pinia/testing@0.0.16...@pinia/testing@0.1.0) (2023-05-08)

### Features

- **testing:** allow mocking $reset ([5f526a3](https://github.com/vuejs/pinia/commit/5f526a33ab0ac441fe865344977a11e0e471ce17)), closes [#2188](https://github.com/vuejs/pinia/issues/2188)

## [0.0.16](https://github.com/vuejs/pinia/compare/@pinia/testing@0.0.15...@pinia/testing@0.0.16) (2023-04-07)

### Bug Fixes

- support "types" condition in "exports" field ([#2078](https://github.com/vuejs/pinia/issues/2078)) ([66d3a5e](https://github.com/vuejs/pinia/commit/66d3a5edd03f28f52daf35449db8c5f660c70b01))
- **testing:** override computed in setup stores ([f9534c9](https://github.com/vuejs/pinia/commit/f9534c926469027f8ccc75c43ce1ea329b58aa0d)), closes [#2109](https://github.com/vuejs/pinia/issues/2109)

## [0.0.15](https://github.com/vuejs/pinia/compare/@pinia/testing@0.0.14...@pinia/testing@0.0.15) (2023-02-20)

No changes in this release

## [0.0.14](https://github.com/vuejs/pinia/compare/@pinia/testing@0.0.13...@pinia/testing@0.0.14) (2022-08-18)

- refactor changes

## [0.0.13](https://github.com/vuejs/pinia/compare/@pinia/testing@0.0.12...@pinia/testing@0.0.13) (2022-07-25)

- doc generation changes

## [0.0.12](https://github.com/vuejs/pinia/compare/@pinia/testing@0.0.11...@pinia/testing@0.0.12) (2022-05-05)

### Bug Fixes

- **testing:** allow overriding plugin computed properties ([ad90fd2](https://github.com/vuejs/pinia/commit/ad90fd24eecca8bd7bff238bcfa039e1a0a7f3d5))
- **testing:** correct order of plugin installation ([0f789fe](https://github.com/vuejs/pinia/commit/0f789fe1591ef8d2d10a8616c7abac8ad09cdf98))
- **testing:** stub actions without app ([2e4f6ca](https://github.com/vuejs/pinia/commit/2e4f6ca2e5ba92bc5ba835ebad4ab325a6428a5f))

## [0.0.11](https://github.com/vuejs/pinia/compare/@pinia/testing@0.0.10...@pinia/testing@0.0.11) (2022-03-31)

### Bug Fixes

- avoid prototype pollution ([e4858f9](https://github.com/vuejs/pinia/commit/e4858f9d5f447ba6162ca9f2472608a8bac3eca7))
- **testing:** enable initialState witohut app ([0a99a75](https://github.com/vuejs/pinia/commit/0a99a7589bed28104e26fccfa4fad007d73f4ca1))
- **testing:** Vue 2 initial state reactive ([#1165](https://github.com/vuejs/pinia/issues/1165)) ([f23af8e](https://github.com/vuejs/pinia/commit/f23af8eac97b055e58908eb76aae684fd68685b5))

### Features

- **testing:** allow overriding computed in tests ([f4db826](https://github.com/vuejs/pinia/commit/f4db8264bd61467fa85f2407aedf23756af4b67c)), closes [#945](https://github.com/vuejs/pinia/issues/945)

## [0.0.10](https://github.com/vuejs/pinia/compare/@pinia/testing@0.0.9...@pinia/testing@0.0.10) (2022-03-14)

### Features

- Automatically vitest globals handling

## [0.0.9](https://github.com/vuejs/pinia/compare/@pinia/testing@0.0.8...@pinia/testing@0.0.9) (2021-12-20)

No code updates in this release

## [0.0.8](https://github.com/vuejs/pinia/compare/@pinia/testing@0.0.7...@pinia/testing@0.0.8) (2021-12-04)

### Features

- can set an initialState for tests ([028e0ca](https://github.com/vuejs/pinia/commit/028e0cae2f46744f90c98914cfca13daa7ce36c1))

## [0.0.7](https://github.com/vuejs/pinia/compare/@pinia/testing@0.0.6...@pinia/testing@0.0.7) (2021-12-01)

### Bug Fixes

- **testing:** preserve non-enumerable properties of pinia instance in createTestingPinia ([#841](https://github.com/vuejs/pinia/issues/841)) ([b130d6f](https://github.com/vuejs/pinia/commit/b130d6f648239293457f347b42a7f1b668748d30))

## [0.0.6](https://github.com/vuejs/pinia/compare/@pinia/testing@0.0.5...@pinia/testing@0.0.6) (2021-11-19)

### Bug Fixes

- **testing:** typo when detecting jest existence ([#811](https://github.com/vuejs/pinia/issues/811)) ([c1fd013](https://github.com/vuejs/pinia/commit/c1fd01350b12b09ce49f923ebc9fee992c2408fd))

## [0.0.5](https://github.com/vuejs/pinia/compare/@pinia/testing@0.0.4...@pinia/testing@0.0.5) (2021-11-03)

Nothing new.

## [0.0.4](https://github.com/vuejs/pinia/compare/@pinia/testing@0.0.3...@pinia/testing@0.0.4) (2021-10-21)

### Bug Fixes

- correct peer deps and deps ([c83677a](https://github.com/vuejs/pinia/commit/c83677a9cf7a1cb20b2e6fed529f3c5500062648))

## [0.0.3](https://github.com/vuejs/pinia/compare/@pinia/testing@0.0.2...@pinia/testing@0.0.3) (2021-09-03)

### Features

- add typedoc ([b98e23d](https://github.com/vuejs/pinia/commit/b98e23d5588925c6a0094a92067a3cc5784e965d))

## [0.0.2](https://github.com/vuejs/pinia/compare/@pinia/testing@0.0.1...@pinia/testing@0.0.2) (2021-08-19)

Small refactor with no effective changes.

## 0.0.1 (2021-08-19)

### Features

- **testing:** add testing package ([fc05376](https://github.com/vuejs/pinia/commit/fc053763752c2b11d7b851f95334034a1f9b8347))
