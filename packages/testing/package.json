{"name": "@pinia/testing", "version": "1.0.2", "description": "Testing module for Pinia", "keywords": ["vue", "vuex", "store", "pinia", "tests", "mock", "testing"], "homepage": "https://pinia.vuejs.org/cookbook/testing.html", "bugs": {"url": "https://github.com/vuejs/pinia/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/pinia.git"}, "funding": "https://github.com/sponsors/posva", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "sideEffects": false, "exports": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/*.js", "dist/*.mjs", "dist/*.d.ts"], "scripts": {"build": "tsup", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . -l @pinia/testing -r 1"}, "devDependencies": {"pinia": "workspace:*", "tsup": "^8.5.0"}, "peerDependencies": {"pinia": ">=3.0.3"}, "publishConfig": {"access": "public"}}